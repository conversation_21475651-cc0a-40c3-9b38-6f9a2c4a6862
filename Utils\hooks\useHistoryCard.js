/**
 * Custom hook for HistoryCard component
 * Manages download status, menu actions, and playback logic
 */

import { useState, useEffect, useCallback } from 'react';
import { ToastAndroid } from 'react-native';
import TrackPlayer from 'react-native-track-player';
import { StorageManager } from '../StorageManager';
import { UnifiedDownloadService } from '../UnifiedDownloadService';
import { HistoryManager } from '../HistoryManager';
import { AddOneSongToPlaylist } from '../../MusicPlayerFunctions';
import { createTrackData, createQueueTrackData, canDownloadSong } from '../TrackDataUtils';

export const useHistoryCard = (song, updateTrack, onRefresh) => {
  const [menuVisible, setMenuVisible] = useState(false);
  const [menuPosition, setMenuPosition] = useState({ top: 0, right: 0 });
  const [isDownloaded, setIsDownloaded] = useState(song.isDownloaded || false);
  const [downloadInProgress, setDownloadInProgress] = useState(false);

  // Check download status on mount and when song changes
  useEffect(() => {
    checkDownloadStatus();
  }, [song.id]);

  const checkDownloadStatus = useCallback(async () => {
    try {
      if (canDownloadSong(song)) {
        const downloaded = await StorageManager.isSongDownloaded(song.id);
        setIsDownloaded(downloaded);
      } else {
        setIsDownloaded(song.isDownloaded || song.sourceType === 'downloaded');
      }
    } catch (error) {
      console.error('Error checking download status:', error);
      setIsDownloaded(false);
    }
  }, [song]);

  const handlePlay = useCallback(async () => {
    try {
      const trackData = await createTrackData(song);
      
      // Clear current queue and play the song
      await TrackPlayer.reset();
      await TrackPlayer.add(trackData);
      await TrackPlayer.play();

      updateTrack();

      // Track the play in history
      await HistoryManager.trackSongPlay(trackData);

    } catch (error) {
      console.error('Error playing song from history:', error);
      ToastAndroid.show('Error playing song', ToastAndroid.SHORT);
    }
  }, [song, updateTrack]);

  const handleMenuPress = useCallback((event) => {
    const { pageY } = event.nativeEvent;
    setMenuPosition({
      top: pageY - 100,
      right: 20
    });
    setMenuVisible(true);
  }, []);

  const closeMenu = useCallback(() => {
    setMenuVisible(false);
  }, []);

  const playNext = useCallback(async () => {
    try {
      const trackData = await createQueueTrackData(song);

      const queue = await TrackPlayer.getQueue();
      const currentIndex = await TrackPlayer.getCurrentTrack();
      
      if (currentIndex === null || queue.length === 0) {
        await TrackPlayer.add(trackData);
        await TrackPlayer.play();
      } else {
        await TrackPlayer.add(trackData, currentIndex + 1);
      }
      
      updateTrack();
      closeMenu();
      ToastAndroid.show('Song will play next', ToastAndroid.SHORT);
    } catch (error) {
      console.error('Error adding song to queue:', error);
      ToastAndroid.show('Error adding to queue', ToastAndroid.SHORT);
    }
  }, [song, updateTrack, closeMenu]);

  const addToPlaylist = useCallback(async () => {
    try {
      const trackData = await createQueueTrackData(song);
      await AddOneSongToPlaylist(trackData);
      closeMenu();
    } catch (error) {
      console.error('Error adding to playlist:', error);
      closeMenu();
      ToastAndroid.show('Error adding to playlist', ToastAndroid.SHORT);
    }
  }, [song, closeMenu]);

  const handleDownload = useCallback(async () => {
    try {
      setDownloadInProgress(true);
      closeMenu();

      const downloadData = {
        id: song.id,
        title: song.title,
        artist: song.artist,
        url: song.url,
        artwork: song.artwork,
        duration: song.duration,
        language: song.language || 'unknown'
      };

      await UnifiedDownloadService.downloadSong(downloadData);
      setIsDownloaded(true);
      ToastAndroid.show('Download completed', ToastAndroid.SHORT);

      if (onRefresh) {
        onRefresh();
      }
    } catch (error) {
      console.error('Error downloading song:', error);
      ToastAndroid.show('Download failed', ToastAndroid.SHORT);
    } finally {
      setDownloadInProgress(false);
    }
  }, [song, closeMenu, onRefresh]);

  return {
    // State
    menuVisible,
    menuPosition,
    isDownloaded,
    downloadInProgress,
    
    // Actions
    handlePlay,
    handleMenuPress,
    closeMenu,
    playNext,
    addToPlaylist,
    handleDownload,
    
    // Utilities
    canDownload: canDownloadSong(song) && !isDownloaded
  };
};
