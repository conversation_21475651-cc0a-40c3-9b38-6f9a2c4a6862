/**
 * Debug component to test history tracking functionality
 * Add this to your app temporarily to verify history tracking works
 */

import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
  Alert
} from 'react-native';
import { useTheme } from '@react-navigation/native';
import { testTrackingManager } from '../../Utils/TrackingTest';
import { HistoryManager } from '../../Utils/HistoryManager';
import TrackingManager from '../../Utils/TrackingManager';

export const HistoryTestComponent = () => {
  const { colors } = useTheme();
  const styles = getStyles(colors);
  const [testResults, setTestResults] = useState([]);
  const [isRunning, setIsRunning] = useState(false);
  const [historyStats, setHistoryStats] = useState(null);

  useEffect(() => {
    loadHistoryStats();
  }, []);

  const loadHistoryStats = async () => {
    try {
      const todayStats = await HistoryManager.getTodayStats();
      const weekStats = await HistoryManager.getWeekStats();
      const totalStats = await HistoryManager.getTotalStats();
      
      setHistoryStats({
        today: todayStats,
        week: weekStats,
        total: totalStats
      });
    } catch (error) {
      console.error('Error loading history stats:', error);
    }
  };

  const addTestResult = (message, success = true) => {
    setTestResults(prev => [...prev, { message, success, timestamp: Date.now() }]);
  };

  const runBasicTest = async () => {
    setIsRunning(true);
    setTestResults([]);
    
    try {
      addTestResult('🧪 Starting basic history test...');
      
      const success = await testTrackingManager();
      
      if (success) {
        addTestResult('✅ All tests passed!', true);
        Alert.alert('Success', 'History tracking test completed successfully!');
      } else {
        addTestResult('❌ Some tests failed', false);
        Alert.alert('Error', 'History tracking test failed. Check console for details.');
      }
      
      // Reload stats after test
      await loadHistoryStats();
      
    } catch (error) {
      addTestResult(`❌ Test error: ${error.message}`, false);
      Alert.alert('Error', `Test failed: ${error.message}`);
    } finally {
      setIsRunning(false);
    }
  };

  const testRealTimeTracking = async () => {
    setIsRunning(true);
    setTestResults([]);
    
    try {
      addTestResult('🧪 Testing real-time tracking...');
      
      const testTrack = {
        id: `realtime_test_${Date.now()}`,
        title: 'Real-time Test Song',
        artist: 'Test Artist',
        artwork: null,
        url: 'test.mp3',
        sourceType: 'online',
        isDownloaded: false,
        isLocal: false
      };

      // Start tracking
      addTestResult('▶️ Starting tracking...');
      const startResult = await TrackingManager.startTracking(testTrack);
      addTestResult(`Start result: ${startResult}`, startResult);

      // Simulate some listening time
      addTestResult('⏱️ Simulating 10 seconds of listening...');
      for (let i = 1; i <= 10; i++) {
        TrackingManager.updateTime(1);
        await new Promise(resolve => setTimeout(resolve, 100)); // Small delay
        if (i % 3 === 0) {
          addTestResult(`  - ${i} seconds tracked`);
        }
      }

      // Force save
      addTestResult('💾 Force saving...');
      await TrackingManager.forceSave();
      addTestResult('✅ Force save completed');

      // Check if data was saved
      const history = await HistoryManager.getListeningHistory();
      if (history[testTrack.id]) {
        const songData = history[testTrack.id];
        addTestResult(`✅ Song found in history: ${songData.totalPlayCount} plays, ${Math.round(songData.totalListeningTime)}s listening time`);
      } else {
        addTestResult('❌ Song not found in history', false);
      }

      // Cleanup
      await TrackingManager.cleanup();
      addTestResult('🧹 Cleanup completed');
      
      await loadHistoryStats();
      
    } catch (error) {
      addTestResult(`❌ Real-time test error: ${error.message}`, false);
    } finally {
      setIsRunning(false);
    }
  };

  const clearTestHistory = async () => {
    try {
      const history = await HistoryManager.getListeningHistory();
      const testKeys = Object.keys(history).filter(key => 
        key.includes('test') || key.includes('Test')
      );
      
      if (testKeys.length > 0) {
        // Note: HistoryManager doesn't have a delete function, 
        // so we'll just show what would be deleted
        Alert.alert(
          'Test History Found', 
          `Found ${testKeys.length} test entries. In a real app, you'd implement a delete function.`
        );
      } else {
        Alert.alert('No Test History', 'No test entries found in history.');
      }
    } catch (error) {
      Alert.alert('Error', `Failed to check test history: ${error.message}`);
    }
  };

  const debugTrackingState = () => {
    TrackingManager.debugTrackingState();
    Alert.alert('Debug Info', 'Check console for detailed tracking state information.');
  };

  return (
    <ScrollView style={styles.container}>
      <Text style={styles.title}>History Tracking Test</Text>
      
      {historyStats && (
        <View style={styles.statsContainer}>
          <Text style={styles.sectionTitle}>Current Stats</Text>
          <Text style={styles.statText}>Today: {historyStats.today.playCount} plays, {Math.round(historyStats.today.listeningTime)}s</Text>
          <Text style={styles.statText}>This Week: {historyStats.week.playCount} plays, {Math.round(historyStats.week.listeningTime)}s</Text>
          <Text style={styles.statText}>Total: {historyStats.total.playCount} plays, {Math.round(historyStats.total.listeningTime)}s</Text>
        </View>
      )}

      <View style={styles.buttonContainer}>
        <TouchableOpacity 
          style={[styles.button, isRunning && styles.buttonDisabled]} 
          onPress={runBasicTest}
          disabled={isRunning}
        >
          <Text style={styles.buttonText}>Run Basic Test</Text>
        </TouchableOpacity>

        <TouchableOpacity 
          style={[styles.button, isRunning && styles.buttonDisabled]} 
          onPress={testRealTimeTracking}
          disabled={isRunning}
        >
          <Text style={styles.buttonText}>Test Real-time Tracking</Text>
        </TouchableOpacity>

        <TouchableOpacity 
          style={styles.button} 
          onPress={debugTrackingState}
        >
          <Text style={styles.buttonText}>Debug Tracking State</Text>
        </TouchableOpacity>

        <TouchableOpacity 
          style={styles.button} 
          onPress={clearTestHistory}
        >
          <Text style={styles.buttonText}>Check Test History</Text>
        </TouchableOpacity>

        <TouchableOpacity 
          style={styles.button} 
          onPress={loadHistoryStats}
        >
          <Text style={styles.buttonText}>Refresh Stats</Text>
        </TouchableOpacity>
      </View>

      {testResults.length > 0 && (
        <View style={styles.resultsContainer}>
          <Text style={styles.sectionTitle}>Test Results</Text>
          {testResults.map((result, index) => (
            <Text 
              key={index} 
              style={[styles.resultText, !result.success && styles.errorText]}
            >
              {result.message}
            </Text>
          ))}
        </View>
      )}
    </ScrollView>
  );
};

const getStyles = (colors) => StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
    backgroundColor: colors.background,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: colors.text,
    marginBottom: 20,
    textAlign: 'center',
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.text,
    marginBottom: 10,
  },
  statsContainer: {
    backgroundColor: colors.card,
    padding: 16,
    borderRadius: 8,
    marginBottom: 20,
  },
  statText: {
    fontSize: 14,
    color: colors.text,
    marginBottom: 4,
  },
  buttonContainer: {
    marginBottom: 20,
  },
  button: {
    backgroundColor: colors.primary,
    padding: 12,
    borderRadius: 8,
    marginBottom: 10,
    alignItems: 'center',
  },
  buttonDisabled: {
    opacity: 0.5,
  },
  buttonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  resultsContainer: {
    backgroundColor: colors.card,
    padding: 16,
    borderRadius: 8,
  },
  resultText: {
    fontSize: 12,
    color: colors.text,
    marginBottom: 4,
    fontFamily: 'monospace',
  },
  errorText: {
    color: '#FF6B6B',
  },
});
