import React, { useContext } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Pressable,
  TouchableOpacity,
  Modal
} from 'react-native';
import { useTheme } from '@react-navigation/native';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import FastImage from 'react-native-fast-image';
import Context from '../../Context/Context';
import { useActiveTrack, usePlaybackState } from 'react-native-track-player';
import { formatListeningTime } from '../../Utils/TimeUtils';
import { getImageSource, formatPlayCount } from '../../Utils/TrackDataUtils';
import { useHistoryCard } from '../../Utils/hooks/useHistoryCard';

export const HistoryCard = ({ song, index, allSongs, onRefresh }) => {
  const { colors, dark } = useTheme();
  const styles = getStyles(colors, dark);
  const { updateTrack } = useContext(Context);
  const currentPlaying = useActiveTrack();
  const playerState = usePlaybackState();

  // Use custom hook for all card logic
  const {
    menuVisible,
    menuPosition,
    isDownloaded,
    downloadInProgress,
    handlePlay,
    handleMenuPress,
    closeMenu,
    playNext,
    addToPlaylist,
    handleDownload,
    canDownload
  } = useHistoryCard(song, updateTrack, onRefresh);

  const isCurrentlyPlaying = currentPlaying?.id === song.id;
  const isPlaying = isCurrentlyPlaying && playerState?.state === 'playing';







  return (
    <View style={styles.container}>
      <Pressable style={styles.songCard} onPress={handlePlay}>
        <FastImage
          source={getImageSource(song)}
          style={styles.artwork}
          resizeMode={FastImage.resizeMode.cover}
        />

        <View style={styles.songInfo}>
          <Text style={styles.title} numberOfLines={1}>
            {song.title}
          </Text>
          <Text style={styles.artist} numberOfLines={1}>
            {song.artist}
          </Text>
          <View style={styles.statsRow}>
            <Text style={styles.playCount}>
              {formatPlayCount(song.totalPlayCount)} plays
            </Text>
            <Text style={styles.separator}>•</Text>
            <Text style={styles.listeningTime}>
              {formatListeningTime(song.totalListeningTime)}
            </Text>
          </View>
        </View>

        {isCurrentlyPlaying && (
          <View style={styles.playingIndicator}>
            <MaterialCommunityIcons
              name={isPlaying ? "volume-high" : "pause"}
              size={16}
              color={colors.primary}
            />
          </View>
        )}

        <TouchableOpacity
          style={styles.menuButton}
          onPress={handleMenuPress}
          hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
        >
          <MaterialCommunityIcons
            name="dots-vertical"
            size={20}
            color={colors.textSecondary}
          />
        </TouchableOpacity>
      </Pressable>

      {/* Menu Modal */}
      <Modal
        visible={menuVisible}
        transparent={true}
        animationType="fade"
        onRequestClose={closeMenu}
      >
        <Pressable style={styles.modalOverlay} onPress={closeMenu}>
          <View style={[styles.menuContainer, { 
            top: menuPosition.top, 
            right: menuPosition.right,
            backgroundColor: dark ? '#1E1E1E' : '#FFFFFF'
          }]}>
            <TouchableOpacity style={styles.menuItem} onPress={playNext}>
              <MaterialCommunityIcons name="play-speed" size={20} color={colors.text} />
              <Text style={[styles.menuText, { color: colors.text }]}>Play next</Text>
            </TouchableOpacity>
            
            <TouchableOpacity style={styles.menuItem} onPress={addToPlaylist}>
              <MaterialCommunityIcons name="playlist-plus" size={20} color={colors.text} />
              <Text style={[styles.menuText, { color: colors.text }]}>Add to playlist</Text>
            </TouchableOpacity>
            
            {/* Show download option only for songs that can be downloaded */}
            {canDownload && (
              <TouchableOpacity
                style={styles.menuItem}
                onPress={handleDownload}
                disabled={downloadInProgress}
              >
                <MaterialCommunityIcons
                  name={downloadInProgress ? "download-circle" : "download"}
                  size={20}
                  color={downloadInProgress ? colors.textSecondary : colors.text}
                />
                <Text style={[styles.menuText, {
                  color: downloadInProgress ? colors.textSecondary : colors.text
                }]}>
                  {downloadInProgress ? 'Downloading...' : 'Download'}
                </Text>
              </TouchableOpacity>
            )}
          </View>
        </Pressable>
      </Modal>
    </View>
  );
};

const getStyles = (colors, dark) => StyleSheet.create({
  container: {
    marginHorizontal: 16,
    marginVertical: 2,
  },
  songCard: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 8,
    backgroundColor: dark ? '#1A1A1A' : '#FFFFFF',
    borderRadius: 6,
    elevation: 1,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  artwork: {
    width: 50,
    height: 50,
    borderRadius: 4,
  },
  songInfo: {
    flex: 1,
    marginLeft: 10,
  },
  title: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.text,
    marginBottom: 2,
  },
  artist: {
    fontSize: 14,
    color: colors.textSecondary,
    marginBottom: 4,
  },
  statsRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  playCount: {
    fontSize: 12,
    color: colors.textSecondary,
  },
  separator: {
    fontSize: 12,
    color: colors.textSecondary,
    marginHorizontal: 6,
  },
  listeningTime: {
    fontSize: 12,
    color: colors.textSecondary,
  },
  playingIndicator: {
    marginRight: 8,
  },
  menuButton: {
    padding: 4,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  menuContainer: {
    position: 'absolute',
    borderRadius: 8,
    elevation: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    minWidth: 150,
  },
  menuItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(255, 255, 255, 0.1)',
  },
  menuText: {
    marginLeft: 12,
    fontSize: 14,
  },
});
