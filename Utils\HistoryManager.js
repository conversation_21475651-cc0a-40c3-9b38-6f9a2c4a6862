import AsyncStorage from '@react-native-async-storage/async-storage';

const STORAGE_KEYS = {
  LISTENING_HISTORY: '@orbit_listening_history'
};

// Cache for better performance
let historyCache = null;
let cacheTimestamp = 0;
const CACHE_DURATION = 3000; // 3 seconds cache (reduced for better responsiveness)

// Get current date in YYYY-MM-DD format
const getCurrentDate = () => {
  return new Date().toISOString().split('T')[0];
};



// Track when a song starts playing - Optimized version
const trackSongPlay = async (songData) => {
  try {
    if (!songData?.id || !songData?.title) {
      console.warn('❌ Invalid song data for tracking:', songData);
      return false;
    }

    const { id, title, artist, artwork, url, sourceType, isDownloaded, isLocal } = songData;
    const currentDate = getCurrentDate();
    const timestamp = Date.now();

    const history = await getListeningHistory();

    // Initialize or update song entry
    if (!history[id]) {
      history[id] = {
        id,
        title,
        artist: artist || 'Unknown Artist',
        artwork: artwork || null,
        url: url || null,
        sourceType: sourceType || 'online',
        isDownloaded: isDownloaded || false,
        isLocal: isLocal || false,
        totalPlayCount: 0,
        totalListeningTime: 0,
        firstPlayed: timestamp,
        lastPlayed: timestamp,
        dailyStats: {}
      };
    } else {
      // Update source type info for existing songs
      if (sourceType) history[id].sourceType = sourceType;
      if (isDownloaded !== undefined) history[id].isDownloaded = isDownloaded;
      if (isLocal !== undefined) history[id].isLocal = isLocal;
    }

    // Update play data
    history[id].lastPlayed = timestamp;
    history[id].totalPlayCount++;

    // Initialize daily stats if needed
    if (!history[id].dailyStats[currentDate]) {
      history[id].dailyStats[currentDate] = {
        playCount: 0,
        listeningTime: 0
      };
    }
    history[id].dailyStats[currentDate].playCount++;

    // Use optimized storage write
    await saveHistoryOptimized(history);

    console.log(`✅ Tracked play: "${title}" - Total: ${history[id].totalPlayCount}`);
    console.log(`📊 Current history keys:`, Object.keys(history));
    return true;

  } catch (error) {
    console.error('❌ Error tracking song play:', error);
    return false;
  }
};

// Optimized storage save function
const saveHistoryOptimized = async (history) => {
  try {
    await AsyncStorage.setItem(STORAGE_KEYS.LISTENING_HISTORY, JSON.stringify(history));
    historyCache = history;
    cacheTimestamp = Date.now();
  } catch (error) {
    console.error('Error saving history:', error);
    throw error;
  }
};

// Track listening time for a song - Optimized with batching
const trackListeningTime = async (songId, listeningTimeSeconds) => {
  try {
    if (!songId || !listeningTimeSeconds || listeningTimeSeconds <= 0) {
      return false;
    }

    const currentDate = getCurrentDate();
    const history = await getListeningHistory();

    if (!history[songId]) {
      console.warn('⚠️ Song not found in history for time tracking:', songId);
      return false;
    }

    // Update total listening time
    history[songId].totalListeningTime += listeningTimeSeconds;

    // Initialize daily stats if needed
    if (!history[songId].dailyStats[currentDate]) {
      history[songId].dailyStats[currentDate] = {
        playCount: 0,
        listeningTime: 0
      };
    }

    // Update daily listening time
    history[songId].dailyStats[currentDate].listeningTime += listeningTimeSeconds;

    // Use optimized storage write
    await saveHistoryOptimized(history);

    console.log(`⏱️ Tracked ${listeningTimeSeconds}s for "${history[songId].title}" - Total: ${Math.round(history[songId].totalListeningTime)}s`);

    return true;

  } catch (error) {
    console.error('❌ Error tracking listening time:', error);
    return false;
  }
};

// Batch time tracking for better performance
const batchTrackListeningTime = async (updates) => {
  try {
    if (!updates || updates.length === 0) return false;

    const currentDate = getCurrentDate();
    const history = await getListeningHistory();
    let hasChanges = false;

    for (const { songId, listeningTimeSeconds } of updates) {
      if (songId && listeningTimeSeconds > 0 && history[songId]) {
        history[songId].totalListeningTime += listeningTimeSeconds;

        if (!history[songId].dailyStats[currentDate]) {
          history[songId].dailyStats[currentDate] = {
            playCount: 0,
            listeningTime: 0
          };
        }

        history[songId].dailyStats[currentDate].listeningTime += listeningTimeSeconds;
        hasChanges = true;
      }
    }

    if (hasChanges) {
      await saveHistoryOptimized(history);
      console.log(`📦 Batch tracked ${updates.length} time updates`);
    }

    return hasChanges;

  } catch (error) {
    console.error('❌ Error in batch time tracking:', error);
    return false;
  }
};

// Get all listening history with caching
const getListeningHistory = async () => {
  try {
    // Use cache if available and fresh
    const now = Date.now();
    if (historyCache && (now - cacheTimestamp) < CACHE_DURATION) {
      return historyCache;
    }

    const historyData = await AsyncStorage.getItem(STORAGE_KEYS.LISTENING_HISTORY);
    const history = historyData ? JSON.parse(historyData) : {};

    // Update cache
    historyCache = history;
    cacheTimestamp = now;

    return history;
  } catch (error) {
    console.error('Error getting listening history:', error);
    return {};
  }
};

// Clear cache when needed
const clearCache = () => {
  historyCache = null;
  cacheTimestamp = 0;
};

// Get history sorted by various criteria
const getSortedHistory = async (sortBy = 'lastPlayed', limit = null) => {
  try {
    const history = await getListeningHistory();
    const historyArray = Object.values(history);

    let sortedHistory;
    switch (sortBy) {
      case 'playCount':
        sortedHistory = historyArray.sort((a, b) => b.totalPlayCount - a.totalPlayCount);
        break;
      case 'listeningTime':
        sortedHistory = historyArray.sort((a, b) => b.totalListeningTime - a.totalListeningTime);
        break;
      case 'firstPlayed':
        sortedHistory = historyArray.sort((a, b) => a.firstPlayed - b.firstPlayed);
        break;
      case 'lastPlayed':
      default:
        sortedHistory = historyArray.sort((a, b) => b.lastPlayed - a.lastPlayed);
        break;
    }

    return limit ? sortedHistory.slice(0, limit) : sortedHistory;
  } catch (error) {
    console.error('Error getting sorted history:', error);
    return [];
  }
};

// Get today's listening stats
const getTodayStats = async () => {
  try {
    const currentDate = getCurrentDate();
    const history = await getListeningHistory();
    
    let totalPlayCount = 0;
    let totalListeningTime = 0;
    let uniqueSongs = 0;

    Object.values(history).forEach(song => {
      if (song.dailyStats[currentDate]) {
        totalPlayCount += song.dailyStats[currentDate].playCount;
        totalListeningTime += song.dailyStats[currentDate].listeningTime;
        if (song.dailyStats[currentDate].playCount > 0) {
          uniqueSongs++;
        }
      }
    });

    return {
      playCount: totalPlayCount,
      listeningTime: totalListeningTime,
      uniqueSongs
    };
  } catch (error) {
    console.error('Error getting today stats:', error);
    return { playCount: 0, listeningTime: 0, uniqueSongs: 0 };
  }
};

// Get this week's listening stats
const getWeekStats = async () => {
  try {
    const today = new Date();
    const history = await getListeningHistory();

    let totalPlayCount = 0;
    let totalListeningTime = 0;
    let uniqueSongs = 0;
    const dailyData = {};

    // Initialize daily data for the current week (Monday to Sunday)
    const currentDay = today.getDay(); // 0 = Sunday, 1 = Monday, etc.
    const mondayOffset = currentDay === 0 ? -6 : 1 - currentDay; // Get Monday of current week

    for (let i = 0; i < 7; i++) {
      const date = new Date(today);
      date.setDate(today.getDate() + mondayOffset + i);
      const dateStr = date.toISOString().split('T')[0];
      dailyData[dateStr] = { playCount: 0, listeningTime: 0 };
    }

    // Calculate week start for filtering
    const weekStartDate = new Date(today);
    weekStartDate.setDate(today.getDate() + mondayOffset);
    const weekStart = weekStartDate.toISOString().split('T')[0];

    Object.values(history).forEach(song => {
      let songPlayedThisWeek = false;

      if (song.dailyStats) {
        Object.keys(song.dailyStats).forEach(date => {
          if (date >= weekStart) {
            const dayStats = song.dailyStats[date];
            totalPlayCount += dayStats.playCount || 0;
            totalListeningTime += dayStats.listeningTime || 0;

            // Add to daily data if this date is in our week
            if (dailyData[date]) {
              dailyData[date].playCount += dayStats.playCount || 0;
              dailyData[date].listeningTime += dayStats.listeningTime || 0;
            }

            if ((dayStats.playCount || 0) > 0) {
              songPlayedThisWeek = true;
            }
          }
        });
      }

      if (songPlayedThisWeek) {
        uniqueSongs++;
      }
    });

    return {
      playCount: totalPlayCount,
      listeningTime: totalListeningTime,
      uniqueSongs,
      dailyData
    };
  } catch (error) {
    console.error('Error getting week stats:', error);
    return { playCount: 0, listeningTime: 0, uniqueSongs: 0, dailyData: {} };
  }
};

// Format time in seconds to readable format
const formatTime = (seconds) => {
  if (seconds < 60) {
    return `${Math.round(seconds)}s`;
  } else if (seconds < 3600) {
    const minutes = Math.floor(seconds / 60);
    return `${minutes}m`;
  } else {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    return minutes > 0 ? `${hours}h ${minutes}m` : `${hours}h`;
  }
};

// Clear old history data (optional cleanup function)
const clearOldHistory = async (daysToKeep = 90) => {
  try {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - daysToKeep);
    const cutoffTimestamp = cutoffDate.getTime();

    const history = await getListeningHistory();
    let cleaned = false;

    Object.keys(history).forEach(songId => {
      if (history[songId].lastPlayed < cutoffTimestamp) {
        delete history[songId];
        cleaned = true;
      } else {
        // Clean old daily stats
        Object.keys(history[songId].dailyStats).forEach(date => {
          if (new Date(date).getTime() < cutoffTimestamp) {
            delete history[songId].dailyStats[date];
            cleaned = true;
          }
        });
      }
    });

    if (cleaned) {
      await AsyncStorage.setItem(STORAGE_KEYS.LISTENING_HISTORY, JSON.stringify(history));
      console.log('Cleaned old history data');
    }
  } catch (error) {
    console.error('Error cleaning old history:', error);
  }
};

// Search history by query
const searchHistory = async (query) => {
  try {
    const history = await getListeningHistory();
    const historyArray = Object.values(history);

    if (!query || !query.trim()) {
      return historyArray.sort((a, b) => b.lastPlayed - a.lastPlayed);
    }

    const searchTerm = query.toLowerCase().trim();
    return historyArray.filter(song =>
      song.title.toLowerCase().includes(searchTerm) ||
      song.artist.toLowerCase().includes(searchTerm)
    ).sort((a, b) => b.lastPlayed - a.lastPlayed);
  } catch (error) {
    console.error('Error searching history:', error);
    return [];
  }
};

// Get total stats (all time)
const getTotalStats = async () => {
  try {
    const history = await getListeningHistory();

    let totalPlayCount = 0;
    let totalListeningTime = 0;
    let totalSongs = Object.keys(history).length;

    Object.values(history).forEach(song => {
      totalPlayCount += song.totalPlayCount;
      totalListeningTime += song.totalListeningTime;
    });

    return {
      playCount: totalPlayCount,
      listeningTime: totalListeningTime,
      totalSongs
    };
  } catch (error) {
    console.error('Error getting total stats:', error);
    return { playCount: 0, listeningTime: 0, totalSongs: 0 };
  }
};

export const HistoryManager = {
  trackSongPlay,
  trackListeningTime,
  batchTrackListeningTime,
  getListeningHistory,
  getSortedHistory,
  getTodayStats,
  getWeekStats,
  getTotalStats,
  searchHistory,
  formatTime,
  clearOldHistory,
  clearCache,
  saveHistoryOptimized
};
