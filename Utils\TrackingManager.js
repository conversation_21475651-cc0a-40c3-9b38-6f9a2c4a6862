import { HistoryManager } from './HistoryManager';
import TrackPlayer from 'react-native-track-player';

/**
 * COMPLETELY REWRITTEN Tracking Manager for listening time
 * This version focuses on debugging and fixing the core tracking issues
 */
class TrackingManager {
  constructor() {
    // Core tracking state
    this.currentTrack = null;
    this.isTracking = false;
    this.trackingStartTime = null;
    this.accumulatedTime = 0;

    // Progress event tracking
    this.lastProgressTime = 0;
    this.progressEventCount = 0;

    // Save management
    this.saveInterval = null;
    this.lastSaveTime = 0;
    this.saveFailureCount = 0;

    // Pending updates for offline/error scenarios
    this.pendingTimeUpdates = [];

    // Configuration
    this.MIN_TRACKING_TIME = 3; // Minimum seconds to track (reduced for testing)
    this.SAVE_INTERVAL = 15000; // Save every 15 seconds (more frequent for testing)
    this.RETRY_ATTEMPTS = 3;
    this.MAX_PENDING_UPDATES = 50;
    this.MAX_BATCH_SIZE = 10;

    // Debug flags
    this.DEBUG_MODE = true;
    this.debugLog('🔧 TrackingManager initialized with debug mode ON');
  }

  /**
   * Enhanced debug logging
   */
  debugLog(message, ...args) {
    if (this.DEBUG_MODE) {
      console.log(`[TRACKING] ${message}`, ...args);
    }
  }

  /**
   * Start tracking a new song with enhanced debugging
   */
  async startTracking(trackData) {
    try {
      this.debugLog('🎯 startTracking called with:', {
        id: trackData?.id,
        title: trackData?.title,
        hasArtist: !!trackData?.artist
      });

      // Stop any existing tracking first
      await this.stopTracking();

      if (!trackData?.id || !trackData?.title) {
        this.debugLog('❌ Invalid track data for tracking:', trackData);
        return false;
      }

      // Track the song play in history
      this.debugLog('📝 Tracking song play in history...');
      const playTracked = await HistoryManager.trackSongPlay(trackData);
      if (!playTracked) {
        this.debugLog('❌ Failed to track song play in history');
        return false;
      }
      this.debugLog('✅ Song play tracked in history');

      // Initialize tracking state
      this.currentTrack = trackData;
      this.trackingStartTime = Date.now();
      this.accumulatedTime = 0;
      this.lastProgressTime = Date.now();
      this.progressEventCount = 0;
      this.isTracking = true;

      // Start the save interval
      this.startSaveInterval();

      this.debugLog(`🎵 Started tracking: "${trackData.title}"`);
      this.debugLog('🔍 Tracking state after start:', this.getTrackingStatus());

      return true;

    } catch (error) {
      this.debugLog('❌ Error starting tracking:', error);
      return false;
    }
  }

  /**
   * Enhanced time update with better debugging
   */
  updateTime(deltaSeconds) {
    this.progressEventCount++;
    const now = Date.now();

    // Debug first few progress events
    if (this.progressEventCount <= 10) {
      this.debugLog(`🔄 Progress event #${this.progressEventCount} - Delta: ${deltaSeconds}s`);
      this.debugLog('  - Is Tracking:', this.isTracking);
      this.debugLog('  - Has Current Track:', !!this.currentTrack);
      this.debugLog('  - Current Track Title:', this.currentTrack?.title);
    }

    if (!this.isTracking || !this.currentTrack) {
      if (this.progressEventCount <= 5) {
        this.debugLog(`⚠️ Progress event but not tracking - isTracking: ${this.isTracking}, hasTrack: ${!!this.currentTrack}`);
      }
      return false;
    }

    // Validate deltaSeconds
    if (deltaSeconds <= 0 || deltaSeconds > 10) {
      if (this.progressEventCount <= 5) {
        this.debugLog(`⚠️ Invalid deltaSeconds: ${deltaSeconds}`);
      }
      return false;
    }

    // Calculate actual time passed since last update
    const timeSinceLastProgress = (now - this.lastProgressTime) / 1000;
    this.lastProgressTime = now;

    // Use the smaller of deltaSeconds or actual time passed (prevents time jumps)
    const timeToAdd = Math.min(deltaSeconds, timeSinceLastProgress, 2); // Max 2 seconds per update

    // Add the time
    this.accumulatedTime += timeToAdd;

    // Enhanced logging every 10 seconds
    if (Math.floor(this.accumulatedTime) % 10 === 0 && this.accumulatedTime >= 10) {
      this.debugLog(`⏱️ Tracking: ${Math.round(this.accumulatedTime)}s for "${this.currentTrack.title}"`);
      this.debugLog(`  - Progress events received: ${this.progressEventCount}`);
      this.debugLog(`  - Time added this update: ${timeToAdd}s`);
    }

    // Force save if too many pending updates
    if (this.pendingTimeUpdates.length >= this.MAX_PENDING_UPDATES) {
      this.debugLog('⚠️ Too many pending updates, forcing save...');
      this.processPendingUpdates().catch(error => {
        this.debugLog('❌ Error in force save:', error);
      });
    }

    return true;
  }

  /**
   * Enhanced pause tracking with debugging
   */
  async pauseTracking() {
    try {
      this.debugLog('⏸️ pauseTracking called');
      this.debugLog('  - Current state:', {
        isTracking: this.isTracking,
        hasTrack: !!this.currentTrack,
        accumulatedTime: this.accumulatedTime
      });

      if (!this.isTracking || !this.currentTrack) {
        this.debugLog('⚠️ Not tracking or no track, skipping pause');
        return;
      }

      // Save accumulated time before pausing
      if (this.accumulatedTime >= this.MIN_TRACKING_TIME) {
        this.debugLog(`💾 Saving ${this.accumulatedTime}s before pause`);
        await this.saveAccumulatedTime();
      } else {
        this.debugLog(`⏭️ Skipping save - only ${this.accumulatedTime}s accumulated (min: ${this.MIN_TRACKING_TIME}s)`);
      }

      this.isTracking = false;
      this.stopSaveInterval();

      this.debugLog(`⏸️ Paused tracking: "${this.currentTrack.title}"`);

    } catch (error) {
      this.debugLog('❌ Error pausing tracking:', error);
    }
  }

  /**
   * Enhanced resume tracking with debugging
   */
  resumeTracking() {
    this.debugLog('▶️ resumeTracking called');

    if (!this.currentTrack) {
      this.debugLog('⚠️ Cannot resume tracking - no current track');
      return false;
    }

    this.isTracking = true;
    this.trackingStartTime = Date.now();
    this.lastProgressTime = Date.now();
    this.startSaveInterval();

    this.debugLog(`▶️ Resumed tracking: "${this.currentTrack.title}"`);
    this.debugLog('🔍 Tracking state after resume:', this.getTrackingStatus());
    return true;
  }

  /**
   * Enhanced stop tracking with debugging
   */
  async stopTracking() {
    try {
      this.debugLog('🛑 stopTracking called');

      if (!this.currentTrack) {
        this.debugLog('⚠️ No current track, nothing to stop');
        return;
      }

      this.debugLog('  - Final state before stop:', {
        accumulatedTime: this.accumulatedTime,
        progressEvents: this.progressEventCount,
        trackTitle: this.currentTrack.title
      });

      // Save any remaining accumulated time
      if (this.accumulatedTime >= this.MIN_TRACKING_TIME) {
        this.debugLog(`💾 Final save: ${this.accumulatedTime}s`);
        await this.saveAccumulatedTime();
      } else {
        this.debugLog(`⏭️ Skipping final save - only ${this.accumulatedTime}s accumulated`);
      }

      // Process any pending batch updates
      if (this.pendingTimeUpdates.length > 0) {
        this.debugLog(`📦 Processing ${this.pendingTimeUpdates.length} pending updates`);
        await this.processPendingUpdates();
      }

      // Reset state
      const trackTitle = this.currentTrack.title;
      this.currentTrack = null;
      this.trackingStartTime = null;
      this.accumulatedTime = 0;
      this.lastProgressTime = 0;
      this.progressEventCount = 0;
      this.isTracking = false;
      this.stopSaveInterval();

      this.debugLog(`🛑 Stopped tracking: "${trackTitle}"`);

    } catch (error) {
      this.debugLog('❌ Error stopping tracking:', error);
    }
  }

  /**
   * Start the periodic save interval
   */
  startSaveInterval() {
    if (this.saveInterval) {
      clearInterval(this.saveInterval);
    }

    this.saveInterval = setInterval(async () => {
      await this.saveAccumulatedTime();
    }, this.SAVE_INTERVAL);
  }

  /**
   * Stop the save interval
   */
  stopSaveInterval() {
    if (this.saveInterval) {
      clearInterval(this.saveInterval);
      this.saveInterval = null;
    }
  }

  /**
   * Enhanced save with better debugging and validation
   */
  async saveAccumulatedTime(retryCount = 0) {
    this.debugLog('💾 saveAccumulatedTime called', {
      isTracking: this.isTracking,
      hasTrack: !!this.currentTrack,
      accumulatedTime: this.accumulatedTime,
      minTime: this.MIN_TRACKING_TIME
    });

    if (!this.currentTrack || this.accumulatedTime < this.MIN_TRACKING_TIME) {
      this.debugLog('⏭️ Skipping save - insufficient time or no track');
      return false;
    }

    try {
      const timeToSave = Math.floor(this.accumulatedTime);
      this.debugLog(`📝 Attempting to save ${timeToSave}s for "${this.currentTrack.title}"`);

      const success = await HistoryManager.trackListeningTime(this.currentTrack.id, timeToSave);

      if (success) {
        this.debugLog(`✅ Successfully saved ${timeToSave}s for "${this.currentTrack.title}"`);
        this.accumulatedTime = 0;
        this.saveFailureCount = 0;
        this.lastSaveTime = Date.now();
        return true;
      } else {
        throw new Error('HistoryManager.trackListeningTime returned false');
      }

    } catch (error) {
      this.saveFailureCount++;
      this.debugLog(`❌ Error saving time (attempt ${retryCount + 1}):`, error);

      // Retry logic
      if (retryCount < this.RETRY_ATTEMPTS) {
        this.debugLog(`🔄 Retrying save in ${(retryCount + 1) * 1000}ms...`);
        await new Promise(resolve => setTimeout(resolve, (retryCount + 1) * 1000));
        return this.saveAccumulatedTime(retryCount + 1);
      }

      // If all retries failed, add to pending updates
      this.debugLog('📦 All retries failed, adding to pending updates');
      this.pendingTimeUpdates.push({
        songId: this.currentTrack.id, // Note: using songId to match HistoryManager
        listeningTimeSeconds: Math.floor(this.accumulatedTime),
        timestamp: Date.now()
      });

      // Reset accumulated time to prevent data loss
      this.accumulatedTime = 0;
      return false;
    }
  }

  /**
   * Enhanced pending updates processing
   */
  async processPendingUpdates() {
    if (this.pendingTimeUpdates.length === 0) {
      return true;
    }

    try {
      const batchSize = Math.min(this.MAX_BATCH_SIZE, this.pendingTimeUpdates.length);
      const batch = this.pendingTimeUpdates.splice(0, batchSize);

      this.debugLog(`📦 Processing batch of ${batch.length} pending updates`);
      const success = await HistoryManager.batchTrackListeningTime(batch);

      if (success) {
        this.debugLog(`✅ Successfully processed ${batch.length} pending updates`);
        return true;
      } else {
        // Put failed batch back at the beginning
        this.pendingTimeUpdates.unshift(...batch);
        this.debugLog('❌ Failed to process batch, keeping in queue');
        return false;
      }
    } catch (error) {
      this.debugLog('❌ Error processing pending updates:', error);
      return false;
    }
  }

  /**
   * Start the periodic save interval
   */
  startSaveInterval() {
    if (this.saveInterval) {
      clearInterval(this.saveInterval);
    }

    this.saveInterval = setInterval(async () => {
      if (this.accumulatedTime >= this.MIN_TRACKING_TIME) {
        this.debugLog('⏰ Periodic save triggered');
        await this.saveAccumulatedTime();
      }
    }, this.SAVE_INTERVAL);

    this.debugLog(`⏰ Save interval started (${this.SAVE_INTERVAL}ms)`);
  }

  /**
   * Stop the save interval
   */
  stopSaveInterval() {
    if (this.saveInterval) {
      clearInterval(this.saveInterval);
      this.saveInterval = null;
      this.debugLog('⏰ Save interval stopped');
    }
  }

  /**
   * Get current tracking status with enhanced info
   */
  getTrackingStatus() {
    return {
      isTracking: this.isTracking,
      currentTrack: this.currentTrack,
      accumulatedTime: this.accumulatedTime,
      trackingStartTime: this.trackingStartTime,
      progressEventCount: this.progressEventCount,
      pendingUpdates: this.pendingTimeUpdates.length,
      saveFailureCount: this.saveFailureCount
    };
  }

  /**
   * Force save current progress
   */
  async forceSave() {
    this.debugLog('🚨 Force save triggered');
    await this.saveAccumulatedTime();
    await this.processPendingUpdates();
  }

  /**
   * Enhanced debug function
   */
  debugTrackingState() {
    this.debugLog('🔍 COMPLETE TRACKING STATE:');
    this.debugLog('  - Is Tracking:', this.isTracking);
    this.debugLog('  - Current Track:', this.currentTrack?.title || 'None');
    this.debugLog('  - Accumulated Time:', Math.round(this.accumulatedTime * 100) / 100, 'seconds');
    this.debugLog('  - Progress Events:', this.progressEventCount);
    this.debugLog('  - Tracking Start Time:', this.trackingStartTime ? new Date(this.trackingStartTime).toLocaleTimeString() : 'None');
    this.debugLog('  - Save Interval Active:', !!this.saveInterval);
    this.debugLog('  - Pending Updates:', this.pendingTimeUpdates.length);
    this.debugLog('  - Save Failures:', this.saveFailureCount);
    this.debugLog('  - Min Tracking Time:', this.MIN_TRACKING_TIME);
    this.debugLog('  - Save Interval:', this.SAVE_INTERVAL);
  }

  /**
   * Global debug function that can be called from anywhere
   */
  static debugGlobal() {
    console.log('🌍 GLOBAL TRACKING DEBUG - Call trackingManager.debugTrackingState() for details');
    if (typeof global !== 'undefined') {
      global.debugTracking = () => trackingManager.debugTrackingState();
      console.log('💡 You can now call debugTracking() from anywhere to see tracking state');
    }
  }

  /**
   * Cleanup - call when app is closing or component unmounting
   */
  async cleanup() {
    this.debugLog('🧹 Cleanup called');
    await this.stopTracking();
    this.stopSaveInterval();
  }
}

// Create singleton instance
const trackingManager = new TrackingManager();

// Setup global debug function
TrackingManager.debugGlobal();

export default trackingManager;
export { TrackingManager };
