import Context from "./Context";
import { useEffect, useState, useRef } from "react";
import { AppState } from "react-native";
import TrackPlayer, { Event, useTrackPlayerEvents } from "react-native-track-player";
import { getRecommendedSongs } from "../Api/Recommended";
import { AddSongsToQueue } from "../MusicPlayerFunctions";
import FormatArtist from "../Utils/FormatArtists";
import { Repeats } from "../Utils/Repeats";
import { SetQueueSongs } from "../LocalStorage/storeQueue";
import { EachSongMenuModal } from "../Component/Global/EachSongMenuModal";
import { CacheManager } from "../Utils/CacheManager";
import TrackingManager from "../Utils/TrackingManager";


const events = [
    Event.PlaybackActiveTrackChanged,
    Event.PlaybackError,
    Event.PlaybackState,
    Event.PlaybackProgressUpdated,
];
const ContextState = (props)=>{
    const [Index, setIndex] = useState(0);
    const [QueueIndex, setQueueIndex] = useState(0);
    const [currentPlaying, setCurrentPlaying]  = useState({})
    const [Repeat, setRepeat] = useState(Repeats.NoRepeat);
    const [Visible, setVisible] = useState({
        visible:false,
    });
    const [previousScreen, setPreviousScreen] = useState(null);
    // Dedicated state for music player navigation - won't be affected by general navigation
    const [musicPreviousScreen, setMusicPreviousScreen] = useState("");
    
    // Add state to track the current playlist information
    const [currentPlaylistData, setCurrentPlaylistData] = useState(null);
    
    // Add state to track liked playlists for UI updates
    const [likedPlaylists, setLikedPlaylists] = useState([]);

    // Progress event counter (use ref to persist across renders)
    const progressEventCount = useRef(0);

    // Simplified tracking state - using TrackingManager
    const [isTrackingInitialized, setIsTrackingInitialized] = useState(false);

    const [Queue, setQueue] = useState([]);
    async function updateTrack (){
        const tracks = await TrackPlayer.getQueue();
        // await SetQueueSongs(tracks)
        // Removed massive queue log that was causing console spam
        const ids = tracks.map((e)=>e.id)
        const queuesId = Queue.map((e)=>e.id)
        if (JSON.stringify(ids) !== JSON.stringify(queuesId)){
            setQueue(tracks)
        }
    }
    
    // Function to update liked playlists state and trigger UI updates
    function updateLikedPlaylist() {
        // This is just to trigger rerenders when playlists are liked/unliked
        setLikedPlaylists(prev => [...prev]);
    }
    
    async function AddRecommendedSongs(index,id){
        const tracks = await TrackPlayer.getQueue();
        const totalTracks = tracks.length - 1
        if (index >= totalTracks - 2){
           try {
               const songs = await getRecommendedSongs(id)
               if (songs?.data?.length !== 0){
                   const ForMusicPlayer = songs.data.map((e)=> {
                       return {
                           url:e.downloadUrl[3].url,
                           title:e.name.toString().replaceAll("&quot;","\"").replaceAll("&amp;","and").replaceAll("&#039;","'").replaceAll("&trade;","™"),
                           artist:FormatArtist(e?.artists?.primary).toString().replaceAll("&quot;","\"").replaceAll("&amp;","and").replaceAll("&#039;","'").replaceAll("&trade;","™"),
                           artwork:e.image[2].url,
                           duration:e.duration,
                           id:e.id,
                           language:e.language,
                       }
                   })
                   await AddSongsToQueue(ForMusicPlayer)
               }
           } catch (e) {
               console.log(e);
           } finally {
               await updateTrack()
           }
        }
    }

    useTrackPlayerEvents(events, async (event) => {
        if (event.type === Event.PlaybackError) {
            console.warn('An error occured while playing the current track.');
        }

        if (event.type === Event.PlaybackProgressUpdated) {
            // Enhanced progress tracking with better debugging
            progressEventCount.current++;

            try {
                // Get current player state to verify we should be tracking
                const playerState = await TrackPlayer.getState();
                const activeTrack = await TrackPlayer.getActiveTrack();

                // Debug first few progress events
                if (progressEventCount.current <= 5) {
                    console.log(`🔄 Progress event #${progressEventCount.current}:`);
                    console.log(`  - Player State: ${playerState}`);
                    console.log(`  - Active Track: ${activeTrack?.title || 'None'}`);
                    console.log(`  - Event Position: ${event.position}s`);
                    console.log(`  - Event Duration: ${event.duration}s`);
                }

                // Only track if player is actually playing
                if (playerState === TrackPlayer.STATE_PLAYING && activeTrack?.id) {
                    // Update with 1 second since progressUpdateEventInterval is set to 1
                    const success = TrackingManager.updateTime(1);

                    // Debug tracking success for first few events
                    if (progressEventCount.current <= 5) {
                        console.log(`  - Tracking Update Success: ${success}`);
                    }
                } else {
                    // Only log first few failures to avoid spam
                    if (progressEventCount.current <= 5) {
                        console.log(`⚠️ Progress event but not playing - State: ${playerState}, Track: ${activeTrack?.title || 'None'}`);
                    }
                }
            } catch (error) {
                console.error('❌ Error in progress tracking:', error);
            }
        }

        if (event.type === Event.PlaybackActiveTrackChanged) {
            console.log('🎵 Track changed:', event.track?.title || 'Unknown');
            setCurrentPlaying(event.track);

            // Use optimized tracking manager
            if (event.track?.id && event.track?.title) {
                const songData = {
                    id: event.track.id,
                    title: event.track.title,
                    artist: event.track.artist || 'Unknown Artist',
                    artwork: event.track.artwork,
                    url: event.track.url,
                    sourceType: event.track.sourceType || (event.track.isDownloaded ? 'downloaded' : (event.track.isLocal ? 'local' : 'online')),
                    isDownloaded: event.track.isDownloaded || false,
                    isLocal: event.track.isLocal || false
                };

                // Start tracking with the new system
                try {
                    const success = await TrackingManager.startTracking(songData);
                    if (success) {
                        console.log('✅ Started tracking:', songData.title);

                        // Check if player is already playing and start tracking immediately
                        const currentState = await TrackPlayer.getState();
                        if (currentState === TrackPlayer.STATE_PLAYING) {
                            console.log('🎵 Song is already playing, ensuring tracking is active');
                            TrackingManager.resumeTracking();
                        }
                    }
                } catch (error) {
                    console.error('❌ Error starting tracking:', error);
                }

                if (Repeat === Repeats.NoRepeat){
                    AddRecommendedSongs(event.index, event.track.id);
                }
            } else {
                // Stop tracking if no track
                try {
                    await TrackingManager.stopTracking();
                } catch (error) {
                    console.error('Error stopping tracking:', error);
                }
            }
        }

        if (event.type === Event.PlaybackState) {
            // Enhanced playback state handling with better debugging
            try {
                console.log(`🎮 Playback state changed to: ${event.state}`);

                if (event.state === 'paused' || event.state === 'stopped') {
                    console.log('⏸️ Pausing tracking due to playback state');
                    await TrackingManager.pauseTracking();
                } else if (event.state === 'playing') {
                    // Check if we have a current track before resuming
                    const status = TrackingManager.getTrackingStatus();
                    if (status.currentTrack) {
                        console.log('▶️ Resuming tracking - playback started');
                        TrackingManager.resumeTracking();
                    } else {
                        console.log('▶️ Playback started but no current track - waiting for track change event');
                    }
                }
            } catch (error) {
                console.error('❌ Error handling playback state:', error);
            }
        }
    });
    async function InitialSetup(){
        try {
            // Clear old cache entries to prevent storage full errors
            await CacheManager.clearOldCacheEntries();

            await TrackPlayer.setupPlayer({
                android: {
                    appKilledPlaybackBehavior: 'StopPlaybackAndRemoveNotification'
                },
                autoHandleInterruptions: true,
                progressUpdateEventInterval: 1 // Update every 1 second
            })
            console.log('Player initialized successfully in Context with progress updates');
        } catch (error) {
            // Ignore the error if player is already initialized
            if (error.message && error.message.includes('player has already been initialized')) {
                console.log('Player already initialized in Context');
            } else {
                console.error('Error initializing player in Context:', error);
            }
        }

        await updateTrack()
        await getCurrentSong()
    }
    async function getCurrentSong(){
        const song = await TrackPlayer.getActiveTrack()
        setCurrentPlaying(song)

        // If there's already a song playing when the app starts, start tracking it
        if (song?.id && song?.title) {
            const state = await TrackPlayer.getState();
            if (state === TrackPlayer.STATE_PLAYING) {
                console.log('🎵 Found playing song on app start, initializing tracking:', song.title);
                const songData = {
                    id: song.id,
                    title: song.title,
                    artist: song.artist || 'Unknown Artist',
                    artwork: song.artwork,
                    url: song.url,
                    sourceType: song.sourceType || (song.isDownloaded ? 'downloaded' : (song.isLocal ? 'local' : 'online')),
                    isDownloaded: song.isDownloaded || false,
                    isLocal: song.isLocal || false
                };

                try {
                    await TrackingManager.startTracking(songData);
                    console.log('✅ Started tracking for existing song:', songData.title);
                } catch (error) {
                    console.error('❌ Error starting tracking for existing song:', error);
                }
            }
        }
    }


    useEffect(() => {
        InitialSetup();

        // Add app state change listener
        const handleAppStateChange = async (nextAppState) => {
            if (nextAppState === 'background' || nextAppState === 'inactive') {
                try {
                    await TrackingManager.forceSave();
                } catch (error) {
                    console.error('Error force saving tracking data:', error);
                }
            }
        };

        const appStateSubscription = AppState.addEventListener('change', handleAppStateChange);

        return () => {
            // Cleanup when component unmounts
            try {
                TrackingManager.cleanup();
            } catch (error) {
                console.error('Error cleaning up tracking manager:', error);
            }
            appStateSubscription?.remove();
        };
    }, []);
    return <Context.Provider value={{
        currentPlaying,  
        Repeat, 
        setRepeat, 
        updateTrack, 
        Index, 
        setIndex, 
        QueueIndex, 
        setQueueIndex, 
        setVisible, 
        Queue, 
        previousScreen, 
        setPreviousScreen,
        musicPreviousScreen,
        setMusicPreviousScreen,
        currentPlaylistData,
        setCurrentPlaylistData,
        updateLikedPlaylist,
        likedPlaylists
    }}>
        {props.children}
         <EachSongMenuModal setVisible={setVisible} Visible={Visible}/>
    </Context.Provider>
}

export default  ContextState
