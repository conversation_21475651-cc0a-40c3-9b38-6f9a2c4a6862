/**
 * Utility functions for creating and managing track data objects
 * Reduces code duplication across components
 */

import { StorageManager } from './StorageManager';

/**
 * Create standardized track data object from song data
 * @param {Object} song - Song object with basic properties
 * @param {Object} options - Additional options for track creation
 * @returns {Promise<Object>} Standardized track data object
 */
export const createTrackData = async (song, options = {}) => {
  if (!song?.id || !song?.title) {
    throw new Error('Invalid song data: missing id or title');
  }

  const baseTrackData = {
    id: song.id,
    title: song.title,
    artist: song.artist || 'Unknown Artist',
    artwork: song.artwork,
    duration: song.duration,
    language: song.language || 'unknown'
  };

  // Handle different source types
  if (song.sourceType === 'downloaded' || song.isDownloaded) {
    try {
      const songPath = await StorageManager.getSongPath(song.id, song.title);
      return {
        ...baseTrackData,
        url: `file://${songPath}`,
        isDownloaded: true,
        sourceType: 'downloaded'
      };
    } catch (pathError) {
      console.error('Error getting downloaded song path:', pathError);
      // Fallback to original URL if path fails
      return {
        ...baseTrackData,
        url: song.url,
        sourceType: 'online'
      };
    }
  } else if (song.sourceType === 'local' || song.isLocal) {
    return {
      ...baseTrackData,
      url: song.url,
      isLocal: true,
      sourceType: 'local'
    };
  } else {
    // Online streaming
    if (!song.url) {
      throw new Error('No URL available for online song');
    }
    return {
      ...baseTrackData,
      url: song.url,
      sourceType: 'online'
    };
  }
};

/**
 * Create track data for queue operations (play next, add to playlist)
 * @param {Object} song - Song object
 * @returns {Promise<Object>} Track data for queue operations
 */
export const createQueueTrackData = async (song) => {
  const trackData = await createTrackData(song);
  
  // For queue operations, we might need additional properties
  return {
    ...trackData,
    // Add any queue-specific properties here if needed
  };
};

/**
 * Create track data for history tracking
 * @param {Object} song - Song object
 * @returns {Promise<Object>} Track data for history tracking
 */
export const createHistoryTrackData = async (song) => {
  const trackData = await createTrackData(song);
  
  // Add history-specific properties
  return {
    ...trackData,
    sourceType: trackData.sourceType,
    isDownloaded: trackData.isDownloaded || false,
    isLocal: trackData.isLocal || false
  };
};

/**
 * Check if a song can be downloaded
 * @param {Object} song - Song object
 * @returns {boolean} True if song can be downloaded
 */
export const canDownloadSong = (song) => {
  return !song.isLocal &&
         !song.isDownloaded &&
         song.sourceType !== 'local' &&
         song.sourceType !== 'downloaded' &&
         song.url &&
         !song.url.startsWith('file://');
};

/**
 * Get appropriate image source for song artwork
 * @param {Object} song - Song object
 * @returns {Object} Image source object for React Native Image components
 */
export const getImageSource = (song) => {
  if (song.artwork) {
    if (typeof song.artwork === 'string') {
      return { uri: song.artwork };
    }
    return song.artwork;
  }
  return { uri: 'https://htmlcolorcodes.com/assets/images/colors/gray-color-solid-background-1920x1080.png' };
};

/**
 * Format play count for display
 * @param {number} count - Play count number
 * @returns {string} Formatted play count string
 */
export const formatPlayCount = (count) => {
  if (!count || count === 0) return '0';
  if (count >= 1000) {
    return `${(count / 1000).toFixed(1)}k`;
  }
  return count.toString();
};

/**
 * Validate track data before operations
 * @param {Object} trackData - Track data object
 * @returns {boolean} True if valid
 */
export const isValidTrackData = (trackData) => {
  return trackData &&
         trackData.id &&
         trackData.title &&
         trackData.url;
};

export default {
  createTrackData,
  createQueueTrackData,
  createHistoryTrackData,
  canDownloadSong,
  getImageSource,
  formatPlayCount,
  isValidTrackData
};
