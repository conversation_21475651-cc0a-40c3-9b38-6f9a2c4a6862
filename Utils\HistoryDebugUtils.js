/**
 * Debug utilities for testing history tracking
 */

import TrackingManager from './TrackingManager';
import { HistoryManager } from './HistoryManager';
import TrackPlayer from 'react-native-track-player';

/**
 * Test if progress events are working
 */
export const testProgressEvents = () => {
  console.log('🧪 Testing progress events...');
  
  // Set up a temporary listener to see if events are firing
  let eventCount = 0;
  const testListener = (event) => {
    if (event.type === 'playback-progress-updated') {
      eventCount++;
      console.log(`✅ Progress event #${eventCount} received:`, {
        position: event.position,
        duration: event.duration
      });
      
      if (eventCount >= 5) {
        // Remove listener after 5 events
        TrackPlayer.removeEventListener('playback-progress-updated', testListener);
        console.log('✅ Progress events are working! Removed test listener.');
      }
    }
  };
  
  TrackPlayer.addEventListener('playback-progress-updated', testListener);
  
  // Remove listener after 10 seconds if no events received
  setTimeout(() => {
    if (eventCount === 0) {
      TrackPlayer.removeEventListener('playback-progress-updated', testListener);
      console.log('❌ No progress events received in 10 seconds. Events may not be working.');
    }
  }, 10000);
  
  console.log('🔍 Test listener added. Play a song to see if progress events fire.');
};

/**
 * Test the tracking system manually
 */
export const testTrackingSystem = async () => {
  try {
    console.log('🧪 Testing tracking system...');
    
    // Get current player state
    const state = await TrackPlayer.getState();
    const activeTrack = await TrackPlayer.getActiveTrack();
    
    console.log('📊 Current player state:', state);
    console.log('📊 Active track:', activeTrack?.title || 'None');
    
    // Test TrackingManager status
    const status = TrackingManager.getTrackingStatus();
    console.log('📊 Tracking status:', status);
    
    if (status.isTracking && status.currentTrack) {
      console.log('✅ Tracking is active');
      
      // Test manual time update
      console.log('🧪 Testing manual time update...');
      const success = TrackingManager.updateTime(5);
      console.log('📊 Manual update result:', success);
      
      // Force save
      console.log('🧪 Testing force save...');
      await TrackingManager.forceSave();
      console.log('✅ Force save completed');
      
      // Check history
      const history = await HistoryManager.getListeningHistory();
      const songData = history[status.currentTrack.id];
      if (songData) {
        console.log('✅ Song found in history:', {
          title: songData.title,
          playCount: songData.totalPlayCount,
          listeningTime: Math.round(songData.totalListeningTime)
        });
      } else {
        console.log('❌ Song not found in history');
      }
    } else {
      console.log('⚠️ Tracking is not active. Play a song first.');
    }
    
    return true;
  } catch (error) {
    console.error('❌ Error testing tracking system:', error);
    return false;
  }
};

/**
 * Get detailed tracking info
 */
export const getTrackingInfo = async () => {
  try {
    console.log('📊 DETAILED TRACKING INFO:');
    
    // TrackPlayer info
    const state = await TrackPlayer.getState();
    const activeTrack = await TrackPlayer.getActiveTrack();
    const queue = await TrackPlayer.getQueue();
    
    console.log('🎵 TrackPlayer:');
    console.log('  - State:', state);
    console.log('  - Active Track:', activeTrack?.title || 'None');
    console.log('  - Queue Length:', queue.length);
    
    // TrackingManager info
    const status = TrackingManager.getTrackingStatus();
    console.log('📈 TrackingManager:');
    console.log('  - Is Tracking:', status.isTracking);
    console.log('  - Current Track:', status.currentTrack?.title || 'None');
    console.log('  - Accumulated Time:', Math.round(status.accumulatedTime * 100) / 100, 'seconds');
    console.log('  - Progress Events:', status.progressEventCount);
    console.log('  - Pending Updates:', status.pendingUpdates);
    
    // History info
    const history = await HistoryManager.getListeningHistory();
    const historyKeys = Object.keys(history);
    console.log('📚 History:');
    console.log('  - Total Songs:', historyKeys.length);
    
    if (historyKeys.length > 0) {
      const recentSongs = Object.values(history)
        .sort((a, b) => b.lastPlayed - a.lastPlayed)
        .slice(0, 3);
      
      console.log('  - Recent Songs:');
      recentSongs.forEach((song, index) => {
        console.log(`    ${index + 1}. ${song.title} - ${song.totalPlayCount} plays, ${Math.round(song.totalListeningTime)}s`);
      });
    }
    
    // Today's stats
    const todayStats = await HistoryManager.getTodayStats();
    console.log('📅 Today\'s Stats:');
    console.log('  - Plays:', todayStats.playCount);
    console.log('  - Listening Time:', Math.round(todayStats.listeningTime), 'seconds');
    console.log('  - Unique Songs:', todayStats.uniqueSongs);
    
    return {
      trackPlayer: { state, activeTrack, queueLength: queue.length },
      tracking: status,
      history: { totalSongs: historyKeys.length },
      today: todayStats
    };
    
  } catch (error) {
    console.error('❌ Error getting tracking info:', error);
    return null;
  }
};

/**
 * Force update history stats (useful for testing)
 */
export const forceUpdateHistory = async () => {
  try {
    console.log('🔄 Force updating history...');
    
    // Clear cache to force fresh data
    HistoryManager.clearCache();
    
    // Get fresh data
    const history = await HistoryManager.getListeningHistory();
    console.log('✅ History refreshed, total songs:', Object.keys(history).length);
    
    return true;
  } catch (error) {
    console.error('❌ Error force updating history:', error);
    return false;
  }
};

// Global debug functions (can be called from console)
if (typeof global !== 'undefined') {
  global.testProgressEvents = testProgressEvents;
  global.testTrackingSystem = testTrackingSystem;
  global.getTrackingInfo = getTrackingInfo;
  global.forceUpdateHistory = forceUpdateHistory;
  
  console.log('🔧 Debug functions available globally:');
  console.log('  - testProgressEvents()');
  console.log('  - testTrackingSystem()');
  console.log('  - getTrackingInfo()');
  console.log('  - forceUpdateHistory()');
}

export default {
  testProgressEvents,
  testTrackingSystem,
  getTrackingInfo,
  forceUpdateHistory
};
