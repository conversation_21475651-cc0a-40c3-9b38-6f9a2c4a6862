// Simple test to verify TrackingManager works
import TrackingManager from './TrackingManager';
import { HistoryManager } from './HistoryManager';

export const testTrackingManager = async () => {
  try {
    console.log('🧪 Testing TrackingManager...');

    // Test basic functionality
    const testTrack = {
      id: 'test123',
      title: 'Test Song',
      artist: 'Test Artist',
      artwork: 'test.jpg',
      url: 'test.mp3',
      sourceType: 'online',
      isDownloaded: false,
      isLocal: false
    };

    // Test start tracking
    console.log('🧪 Testing startTracking...');
    const result = await TrackingManager.startTracking(testTrack);
    console.log('✅ Start tracking result:', result);

    // Test get status
    console.log('🧪 Testing getTrackingStatus...');
    const status = TrackingManager.getTrackingStatus();
    console.log('✅ Tracking status:', status);

    // Test update time
    console.log('🧪 Testing updateTime...');
    TrackingManager.updateTime(5);
    console.log('✅ Updated time by 5 seconds');

    // Wait a bit and check status again
    await new Promise(resolve => setTimeout(resolve, 1000));
    const statusAfter = TrackingManager.getTrackingStatus();
    console.log('✅ Status after update:', statusAfter);

    // Test force save
    console.log('🧪 Testing forceSave...');
    await TrackingManager.forceSave();
    console.log('✅ Force save completed');

    // Test HistoryManager directly
    console.log('🧪 Testing HistoryManager directly...');
    const historyResult = await HistoryManager.trackSongPlay(testTrack);
    console.log('✅ HistoryManager trackSongPlay result:', historyResult);

    const timeResult = await HistoryManager.trackListeningTime('test123', 10);
    console.log('✅ HistoryManager trackListeningTime result:', timeResult);

    // Get history to verify
    const history = await HistoryManager.getListeningHistory();
    console.log('✅ Current history:', Object.keys(history));
    if (history['test123']) {
      console.log('✅ Test song data:', history['test123']);
    }

    // Test cleanup
    console.log('🧪 Testing cleanup...');
    await TrackingManager.cleanup();
    console.log('✅ Cleanup completed');

    console.log('🎉 TrackingManager test completed successfully!');
    return true;

  } catch (error) {
    console.error('❌ TrackingManager test failed:', error);
    return false;
  }
};
